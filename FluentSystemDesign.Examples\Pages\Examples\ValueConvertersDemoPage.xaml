<Page x:Class="FluentSystemDesign.Examples.Pages.Examples.ValueConvertersDemoPage"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:converters="clr-namespace:FluentSystemDesign.WPF.Converters;assembly=FluentSystemDesign.WPF"
      Title="值转换器演示">

    <Page.Resources>
        <!-- 转换器资源 -->
        <converters:BoolToVisibilityConverter x:Key="BoolToVisibilityConverter" />
        <converters:InverseBoolConverter x:Key="InverseBoolConverter" />
        <converters:BoolToOpacityConverter x:Key="BoolToOpacityConverter" />
        <converters:NumberToStringConverter x:Key="NumberToStringConverter" />
        <converters:PercentageConverter x:Key="PercentageConverter" />
        <converters:ThicknessConverter x:Key="ThicknessConverter" />
        <converters:ColorToBrushConverter x:Key="ColorToBrushConverter" />
        <converters:HexToColorConverter x:Key="HexToColorConverter" />
        <converters:ColorToContrastConverter x:Key="ColorToContrastConverter" />
        <converters:EnumToStringConverter x:Key="EnumToStringConverter" />
        <converters:EnumToBoolConverter x:Key="EnumToBoolConverter" />
        <converters:EnumToVisibilityConverter x:Key="EnumToVisibilityConverter" />
        <converters:CollectionToVisibilityConverter x:Key="CollectionToVisibilityConverter" />
        <converters:CountToVisibilityConverter x:Key="CountToVisibilityConverter" />
        <converters:IsNullOrEmptyConverter x:Key="IsNullOrEmptyConverter" />
        <converters:StringToUpperConverter x:Key="StringToUpperConverter" />
        <converters:StringToLowerConverter x:Key="StringToLowerConverter" />
        <converters:StringFormatConverter x:Key="StringFormatConverter" />
        <converters:ThemeToColorConverter x:Key="ThemeToColorConverter" />
        <converters:ThemeToBrushConverter x:Key="ThemeToBrushConverter" />
    </Page.Resources>

    <ScrollViewer>
        <StackPanel Margin="20" Spacing="20">
            
            <!-- 页面标题 -->
            <TextBlock Text="值转换器演示" FontSize="32" FontWeight="Bold" Margin="0,0,0,20"/>
            
            <!-- 布尔转换器演示 -->
            <Border Background="{DynamicResource SurfaceBrush}" 
                    BorderBrush="{DynamicResource BorderBrush}" 
                    BorderThickness="1" 
                    CornerRadius="8" 
                    Padding="20">
                <StackPanel Spacing="15">
                    <TextBlock Text="布尔转换器" FontSize="20" FontWeight="SemiBold"/>
                    
                    <StackPanel Orientation="Horizontal" Spacing="10">
                        <CheckBox x:Name="BoolTestCheckBox" Content="测试布尔值" IsChecked="True"/>
                    </StackPanel>
                    
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <!-- BoolToVisibilityConverter -->
                        <StackPanel Grid.Column="0" Spacing="5">
                            <TextBlock Text="BoolToVisibilityConverter" FontWeight="SemiBold"/>
                            <TextBlock Text="正常模式:" 
                                       Visibility="{Binding IsChecked, ElementName=BoolTestCheckBox, Converter={StaticResource BoolToVisibilityConverter}}"/>
                            <TextBlock Text="反转模式:" 
                                       Visibility="{Binding IsChecked, ElementName=BoolTestCheckBox, Converter={StaticResource BoolToVisibilityConverter}, ConverterParameter=Invert}"/>
                        </StackPanel>
                        
                        <!-- InverseBoolConverter -->
                        <StackPanel Grid.Column="1" Spacing="5">
                            <TextBlock Text="InverseBoolConverter" FontWeight="SemiBold"/>
                            <Button Content="按钮" 
                                    IsEnabled="{Binding IsChecked, ElementName=BoolTestCheckBox, Converter={StaticResource InverseBoolConverter}}"/>
                        </StackPanel>
                        
                        <!-- BoolToOpacityConverter -->
                        <StackPanel Grid.Column="2" Spacing="5">
                            <TextBlock Text="BoolToOpacityConverter" FontWeight="SemiBold"/>
                            <Rectangle Width="100" Height="50" Fill="Blue" 
                                       Opacity="{Binding IsChecked, ElementName=BoolTestCheckBox, Converter={StaticResource BoolToOpacityConverter}}"/>
                        </StackPanel>
                    </Grid>
                </StackPanel>
            </Border>
            
            <!-- 数值转换器演示 -->
            <Border Background="{DynamicResource SurfaceBrush}" 
                    BorderBrush="{DynamicResource BorderBrush}" 
                    BorderThickness="1" 
                    CornerRadius="8" 
                    Padding="20">
                <StackPanel Spacing="15">
                    <TextBlock Text="数值转换器" FontSize="20" FontWeight="SemiBold"/>
                    
                    <StackPanel Orientation="Horizontal" Spacing="10">
                        <TextBlock Text="数值:" VerticalAlignment="Center"/>
                        <Slider x:Name="NumberSlider" Minimum="0" Maximum="1000" Value="123.45" Width="200"/>
                        <TextBlock Text="{Binding Value, ElementName=NumberSlider}" VerticalAlignment="Center"/>
                    </StackPanel>
                    
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <!-- NumberToStringConverter -->
                        <StackPanel Grid.Column="0" Spacing="5">
                            <TextBlock Text="NumberToStringConverter" FontWeight="SemiBold"/>
                            <TextBlock Text="{Binding Value, ElementName=NumberSlider, Converter={StaticResource NumberToStringConverter}, ConverterParameter=C2}"/>
                            <TextBlock Text="{Binding Value, ElementName=NumberSlider, Converter={StaticResource NumberToStringConverter}, ConverterParameter=N0}"/>
                        </StackPanel>
                        
                        <!-- PercentageConverter -->
                        <StackPanel Grid.Column="1" Spacing="5">
                            <TextBlock Text="PercentageConverter" FontWeight="SemiBold"/>
                            <TextBlock Text="{Binding Value, ElementName=NumberSlider, Converter={StaticResource PercentageConverter}, ConverterParameter=0-100}"/>
                        </StackPanel>
                        
                        <!-- ThicknessConverter -->
                        <StackPanel Grid.Column="2" Spacing="5">
                            <TextBlock Text="ThicknessConverter" FontWeight="SemiBold"/>
                            <Border Background="LightBlue" Width="100" Height="50"
                                    BorderThickness="{Binding Value, ElementName=NumberSlider, Converter={StaticResource ThicknessConverter}, ConverterParameter=Left}"
                                    BorderBrush="Blue"/>
                        </StackPanel>
                    </Grid>
                </StackPanel>
            </Border>
            
            <!-- 颜色转换器演示 -->
            <Border Background="{DynamicResource SurfaceBrush}" 
                    BorderBrush="{DynamicResource BorderBrush}" 
                    BorderThickness="1" 
                    CornerRadius="8" 
                    Padding="20">
                <StackPanel Spacing="15">
                    <TextBlock Text="颜色转换器" FontSize="20" FontWeight="SemiBold"/>
                    
                    <StackPanel Orientation="Horizontal" Spacing="10">
                        <TextBlock Text="十六进制颜色:" VerticalAlignment="Center"/>
                        <TextBox x:Name="HexColorTextBox" Text="#FF5722" Width="100"/>
                    </StackPanel>
                    
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <!-- ColorToBrushConverter -->
                        <StackPanel Grid.Column="0" Spacing="5">
                            <TextBlock Text="ColorToBrushConverter" FontWeight="SemiBold"/>
                            <Rectangle Width="100" Height="50" 
                                       Fill="{Binding Text, ElementName=HexColorTextBox, Converter={StaticResource ColorToBrushConverter}}"/>
                        </StackPanel>
                        
                        <!-- HexToColorConverter -->
                        <StackPanel Grid.Column="1" Spacing="5">
                            <TextBlock Text="HexToColorConverter" FontWeight="SemiBold"/>
                            <Rectangle Width="100" Height="50">
                                <Rectangle.Fill>
                                    <SolidColorBrush Color="{Binding Text, ElementName=HexColorTextBox, Converter={StaticResource HexToColorConverter}}"/>
                                </Rectangle.Fill>
                            </Rectangle>
                        </StackPanel>
                        
                        <!-- ColorToContrastConverter -->
                        <StackPanel Grid.Column="2" Spacing="5">
                            <TextBlock Text="ColorToContrastConverter" FontWeight="SemiBold"/>
                            <Border Width="100" Height="50" 
                                    Background="{Binding Text, ElementName=HexColorTextBox, Converter={StaticResource ColorToBrushConverter}}">
                                <TextBlock Text="对比文本" 
                                           HorizontalAlignment="Center" 
                                           VerticalAlignment="Center"
                                           Foreground="{Binding Text, ElementName=HexColorTextBox, Converter={StaticResource ColorToContrastConverter}}"/>
                            </Border>
                        </StackPanel>
                    </Grid>
                </StackPanel>
            </Border>
            
            <!-- 字符串转换器演示 -->
            <Border Background="{DynamicResource SurfaceBrush}" 
                    BorderBrush="{DynamicResource BorderBrush}" 
                    BorderThickness="1" 
                    CornerRadius="8" 
                    Padding="20">
                <StackPanel Spacing="15">
                    <TextBlock Text="字符串转换器" FontSize="20" FontWeight="SemiBold"/>
                    
                    <StackPanel Orientation="Horizontal" Spacing="10">
                        <TextBlock Text="输入文本:" VerticalAlignment="Center"/>
                        <TextBox x:Name="StringTestTextBox" Text="Hello World" Width="200"/>
                    </StackPanel>
                    
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <!-- StringToUpperConverter -->
                        <StackPanel Grid.Column="0" Spacing="5">
                            <TextBlock Text="StringToUpperConverter" FontWeight="SemiBold"/>
                            <TextBlock Text="{Binding Text, ElementName=StringTestTextBox, Converter={StaticResource StringToUpperConverter}}"/>
                            <TextBlock Text="{Binding Text, ElementName=StringTestTextBox, Converter={StaticResource StringToUpperConverter}, ConverterParameter=Words}"/>
                        </StackPanel>
                        
                        <!-- StringToLowerConverter -->
                        <StackPanel Grid.Column="1" Spacing="5">
                            <TextBlock Text="StringToLowerConverter" FontWeight="SemiBold"/>
                            <TextBlock Text="{Binding Text, ElementName=StringTestTextBox, Converter={StaticResource StringToLowerConverter}}"/>
                            <TextBlock Text="{Binding Text, ElementName=StringTestTextBox, Converter={StaticResource StringToLowerConverter}, ConverterParameter=CamelCase}"/>
                        </StackPanel>
                        
                        <!-- StringFormatConverter -->
                        <StackPanel Grid.Column="2" Spacing="5">
                            <TextBlock Text="StringFormatConverter" FontWeight="SemiBold"/>
                            <TextBlock Text="{Binding Text, ElementName=StringTestTextBox, Converter={StaticResource StringFormatConverter}, ConverterParameter='格式化: {0}'}"/>
                        </StackPanel>
                    </Grid>
                </StackPanel>
            </Border>
            
            <!-- 集合转换器演示 -->
            <Border Background="{DynamicResource SurfaceBrush}" 
                    BorderBrush="{DynamicResource BorderBrush}" 
                    BorderThickness="1" 
                    CornerRadius="8" 
                    Padding="20">
                <StackPanel Spacing="15">
                    <TextBlock Text="集合转换器" FontSize="20" FontWeight="SemiBold"/>
                    
                    <StackPanel Orientation="Horizontal" Spacing="10">
                        <TextBlock Text="集合项数量:" VerticalAlignment="Center"/>
                        <Slider x:Name="CountSlider" Minimum="0" Maximum="10" Value="3" Width="200"/>
                        <TextBlock Text="{Binding Value, ElementName=CountSlider}" VerticalAlignment="Center"/>
                    </StackPanel>
                    
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <!-- CountToVisibilityConverter -->
                        <StackPanel Grid.Column="0" Spacing="5">
                            <TextBlock Text="CountToVisibilityConverter" FontWeight="SemiBold"/>
                            <TextBlock Text="大于0时显示" 
                                       Visibility="{Binding Value, ElementName=CountSlider, Converter={StaticResource CountToVisibilityConverter}, ConverterParameter=>0}"/>
                            <TextBlock Text="大于等于5时显示" 
                                       Visibility="{Binding Value, ElementName=CountSlider, Converter={StaticResource CountToVisibilityConverter}, ConverterParameter=>=5}"/>
                        </StackPanel>
                        
                        <!-- IsNullOrEmptyConverter -->
                        <StackPanel Grid.Column="1" Spacing="5">
                            <TextBlock Text="IsNullOrEmptyConverter" FontWeight="SemiBold"/>
                            <TextBlock Text="有内容时显示" 
                                       Visibility="{Binding Text, ElementName=StringTestTextBox, Converter={StaticResource IsNullOrEmptyConverter}, ConverterParameter=Invert}"/>
                        </StackPanel>
                    </Grid>
                </StackPanel>
            </Border>
            
        </StackPanel>
    </ScrollViewer>
</Page>
