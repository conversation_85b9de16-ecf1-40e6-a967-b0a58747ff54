<?xml version="1.0"?>
<doc>
    <assembly>
        <name>FluentSystemDesign.WPF</name>
    </assembly>
    <members>
        <member name="T:FluentSystemDesign.WPF.Behaviors.AnimationBehavior">
            <summary>
            动画行为，为UI元素提供常用的动画效果
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.AnimationBehavior.AnimationTypeProperty">
            <summary>
            动画类型
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.AnimationBehavior.DurationProperty">
            <summary>
            动画持续时间
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.AnimationBehavior.DelayProperty">
            <summary>
            动画延迟时间
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.AnimationBehavior.EasingFunctionProperty">
            <summary>
            缓动函数
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.AnimationBehavior.AutoStartProperty">
            <summary>
            是否自动开始动画
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.AnimationBehavior.TriggerProperty">
            <summary>
            触发器
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.AnimationBehavior.AnimationType">
            <summary>
            获取或设置动画类型
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.AnimationBehavior.Duration">
            <summary>
            获取或设置动画持续时间
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.AnimationBehavior.Delay">
            <summary>
            获取或设置动画延迟时间
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.AnimationBehavior.EasingFunction">
            <summary>
            获取或设置缓动函数
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.AnimationBehavior.AutoStart">
            <summary>
            获取或设置是否自动开始动画
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.AnimationBehavior.Trigger">
            <summary>
            获取或设置触发器
            </summary>
        </member>
        <member name="E:FluentSystemDesign.WPF.Behaviors.AnimationBehavior.AnimationStarted">
            <summary>
            动画开始事件
            </summary>
        </member>
        <member name="E:FluentSystemDesign.WPF.Behaviors.AnimationBehavior.AnimationCompleted">
            <summary>
            动画完成事件
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.AnimationBehavior.OnAttached">
            <summary>
            附加到关联对象时调用
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.AnimationBehavior.OnDetaching">
            <summary>
            从关联对象分离时调用
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.AnimationBehavior.StartAnimation">
            <summary>
            开始动画
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.AnimationBehavior.StopAnimation">
            <summary>
            停止动画
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.AnimationBehavior.CreateStoryboard">
            <summary>
            创建故事板
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.AnimationBehavior.EnsureTransformGroup">
            <summary>
            确保元素有TransformGroup，如果没有则创建
            </summary>
            <returns>TransformGroup实例</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.AnimationBehavior.GetOrCreateTranslateTransform(System.Windows.Media.TransformGroup)">
            <summary>
            获取或创建TranslateTransform
            </summary>
            <param name="transformGroup">Transform组</param>
            <returns>TranslateTransform实例</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.AnimationBehavior.GetOrCreateScaleTransform(System.Windows.Media.TransformGroup)">
            <summary>
            获取或创建ScaleTransform
            </summary>
            <param name="transformGroup">Transform组</param>
            <returns>ScaleTransform实例</returns>
        </member>
        <member name="T:FluentSystemDesign.WPF.Behaviors.AnimationType">
            <summary>
            动画类型枚举
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.AnimationType.FadeIn">
            <summary>
            淡入
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.AnimationType.FadeOut">
            <summary>
            淡出
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.AnimationType.SlideInFromLeft">
            <summary>
            从左侧滑入
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.AnimationType.SlideInFromRight">
            <summary>
            从右侧滑入
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.AnimationType.SlideInFromTop">
            <summary>
            从顶部滑入
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.AnimationType.SlideInFromBottom">
            <summary>
            从底部滑入
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.AnimationType.ScaleIn">
            <summary>
            缩放进入
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.AnimationType.ScaleOut">
            <summary>
            缩放退出
            </summary>
        </member>
        <member name="T:FluentSystemDesign.WPF.Behaviors.AnimationTrigger">
            <summary>
            动画触发器枚举
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.AnimationTrigger.Loaded">
            <summary>
            加载时触发
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.AnimationTrigger.MouseEnter">
            <summary>
            鼠标进入时触发
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.AnimationTrigger.MouseLeave">
            <summary>
            鼠标离开时触发
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.AnimationTrigger.Manual">
            <summary>
            手动触发
            </summary>
        </member>
        <member name="T:FluentSystemDesign.WPF.Behaviors.BehaviorExtensions">
            <summary>
            行为扩展方法，提供便捷的行为附加方式
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.BehaviorExtensions.AddDragBehavior(System.Windows.FrameworkElement,System.Object,System.Windows.DragDropEffects)">
            <summary>
            为元素添加拖拽行为
            </summary>
            <param name="element">目标元素</param>
            <param name="dragData">拖拽数据</param>
            <param name="dragEffects">拖拽效果</param>
            <returns>拖拽行为实例</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.BehaviorExtensions.AddAnimationBehavior(System.Windows.FrameworkElement,FluentSystemDesign.WPF.Behaviors.AnimationType,FluentSystemDesign.WPF.Behaviors.AnimationTrigger,System.Boolean)">
            <summary>
            为元素添加动画行为
            </summary>
            <param name="element">目标元素</param>
            <param name="animationType">动画类型</param>
            <param name="trigger">触发器</param>
            <param name="autoStart">是否自动开始</param>
            <returns>动画行为实例</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.BehaviorExtensions.AddFadeInAnimation(System.Windows.FrameworkElement,System.Boolean)">
            <summary>
            为元素添加淡入动画
            </summary>
            <param name="element">目标元素</param>
            <param name="autoStart">是否自动开始</param>
            <returns>动画行为实例</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.BehaviorExtensions.AddSlideInAnimation(System.Windows.FrameworkElement,FluentSystemDesign.WPF.Behaviors.SlideDirection,System.Boolean)">
            <summary>
            为元素添加滑入动画
            </summary>
            <param name="element">目标元素</param>
            <param name="direction">滑入方向</param>
            <param name="autoStart">是否自动开始</param>
            <returns>动画行为实例</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.BehaviorExtensions.AddFocusManagementBehavior(System.Windows.FrameworkElement,System.Boolean,System.Boolean)">
            <summary>
            为元素添加焦点管理行为
            </summary>
            <param name="element">目标元素</param>
            <param name="focusOnLoad">是否在加载时获取焦点</param>
            <param name="selectAllOnFocus">是否在获取焦点时选择所有文本</param>
            <returns>焦点管理行为实例</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.BehaviorExtensions.AddValidationBehavior(System.Windows.FrameworkElement,FluentSystemDesign.WPF.Behaviors.ValidationType,System.Boolean,System.String)">
            <summary>
            为元素添加验证行为
            </summary>
            <param name="element">目标元素</param>
            <param name="validationType">验证类型</param>
            <param name="isRequired">是否必填</param>
            <param name="errorMessage">错误消息</param>
            <returns>验证行为实例</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.BehaviorExtensions.AddEmailValidation(System.Windows.FrameworkElement,System.Boolean)">
            <summary>
            为元素添加邮箱验证
            </summary>
            <param name="element">目标元素</param>
            <param name="isRequired">是否必填</param>
            <returns>验证行为实例</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.BehaviorExtensions.AddPhoneValidation(System.Windows.FrameworkElement,System.Boolean)">
            <summary>
            为元素添加手机号验证
            </summary>
            <param name="element">目标元素</param>
            <param name="isRequired">是否必填</param>
            <returns>验证行为实例</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.BehaviorExtensions.AddWatermarkBehavior(System.Windows.FrameworkElement,System.String,System.Boolean)">
            <summary>
            为元素添加水印行为
            </summary>
            <param name="element">目标元素</param>
            <param name="watermarkText">水印文本</param>
            <param name="hideOnFocus">是否在获得焦点时隐藏</param>
            <returns>水印行为实例</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.BehaviorExtensions.AddWindowBehavior(System.Windows.Window,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            为窗口添加窗口行为
            </summary>
            <param name="window">目标窗口</param>
            <param name="enableDragMove">是否启用拖拽移动</param>
            <param name="enableDoubleClickMaximize">是否启用双击最大化</param>
            <param name="saveWindowPlacement">是否保存窗口位置</param>
            <returns>窗口行为实例</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.BehaviorExtensions.RemoveBehavior``1(System.Windows.DependencyObject)">
            <summary>
            移除指定类型的行为
            </summary>
            <typeparam name="T">行为类型</typeparam>
            <param name="element">目标元素</param>
            <returns>是否成功移除</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.BehaviorExtensions.GetBehavior``1(System.Windows.DependencyObject)">
            <summary>
            获取指定类型的行为
            </summary>
            <typeparam name="T">行为类型</typeparam>
            <param name="element">目标元素</param>
            <returns>行为实例，如果不存在则返回null</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.BehaviorExtensions.ClearBehaviors(System.Windows.DependencyObject)">
            <summary>
            清除所有行为
            </summary>
            <param name="element">目标元素</param>
        </member>
        <member name="T:FluentSystemDesign.WPF.Behaviors.SlideDirection">
            <summary>
            滑入方向枚举
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.SlideDirection.Left">
            <summary>
            从左侧滑入
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.SlideDirection.Right">
            <summary>
            从右侧滑入
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.SlideDirection.Top">
            <summary>
            从顶部滑入
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.SlideDirection.Bottom">
            <summary>
            从底部滑入
            </summary>
        </member>
        <member name="T:FluentSystemDesign.WPF.Behaviors.DragBehavior">
            <summary>
            拖拽行为，为UI元素提供拖拽功能
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.DragBehavior.IsEnabledProperty">
            <summary>
            是否启用拖拽
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.DragBehavior.DragDataProperty">
            <summary>
            拖拽数据
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.DragBehavior.DragEffectsProperty">
            <summary>
            拖拽效果
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.DragBehavior.DragThresholdProperty">
            <summary>
            拖拽开始阈值（像素）
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.DragBehavior.IsEnabled">
            <summary>
            获取或设置是否启用拖拽
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.DragBehavior.DragData">
            <summary>
            获取或设置拖拽数据
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.DragBehavior.DragEffects">
            <summary>
            获取或设置拖拽效果
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.DragBehavior.DragThreshold">
            <summary>
            获取或设置拖拽开始阈值
            </summary>
        </member>
        <member name="E:FluentSystemDesign.WPF.Behaviors.DragBehavior.DragStarted">
            <summary>
            拖拽开始事件
            </summary>
        </member>
        <member name="E:FluentSystemDesign.WPF.Behaviors.DragBehavior.DragCompleted">
            <summary>
            拖拽完成事件
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.DragBehavior.OnAttached">
            <summary>
            附加到关联对象时调用
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.DragBehavior.OnDetaching">
            <summary>
            从关联对象分离时调用
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.DragBehavior.OnMouseLeftButtonDown(System.Object,System.Windows.Input.MouseButtonEventArgs)">
            <summary>
            鼠标左键按下事件处理
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.DragBehavior.OnMouseMove(System.Object,System.Windows.Input.MouseEventArgs)">
            <summary>
            鼠标移动事件处理
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.DragBehavior.OnMouseLeftButtonUp(System.Object,System.Windows.Input.MouseButtonEventArgs)">
            <summary>
            鼠标左键释放事件处理
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.DragBehavior.StartDrag">
            <summary>
            开始拖拽操作
            </summary>
        </member>
        <member name="T:FluentSystemDesign.WPF.Behaviors.DragStartedEventArgs">
            <summary>
            拖拽开始事件参数
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.DragStartedEventArgs.Data">
            <summary>
            拖拽数据
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.DragStartedEventArgs.Cancel">
            <summary>
            是否取消拖拽
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.DragStartedEventArgs.#ctor(System.Object)">
            <summary>
            构造函数
            </summary>
            <param name="data">拖拽数据</param>
        </member>
        <member name="T:FluentSystemDesign.WPF.Behaviors.DragCompletedEventArgs">
            <summary>
            拖拽完成事件参数
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.DragCompletedEventArgs.Data">
            <summary>
            拖拽数据
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.DragCompletedEventArgs.Result">
            <summary>
            拖拽结果
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.DragCompletedEventArgs.#ctor(System.Object,System.Windows.DragDropEffects)">
            <summary>
            构造函数
            </summary>
            <param name="data">拖拽数据</param>
            <param name="result">拖拽结果</param>
        </member>
        <member name="T:FluentSystemDesign.WPF.Behaviors.FocusManagementBehavior">
            <summary>
            焦点管理行为，提供高级的焦点管理功能
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.FocusManagementBehavior.FocusOnLoadProperty">
            <summary>
            是否在加载时自动获取焦点
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.FocusManagementBehavior.FocusOnMouseEnterProperty">
            <summary>
            是否在鼠标进入时获取焦点
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.FocusManagementBehavior.SelectAllOnFocusProperty">
            <summary>
            是否选择所有文本（仅适用于TextBox）
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.FocusManagementBehavior.TabIndexProperty">
            <summary>
            Tab键导航顺序
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.FocusManagementBehavior.IsTabStopProperty">
            <summary>
            是否参与Tab键导航
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.FocusManagementBehavior.FocusDelayProperty">
            <summary>
            焦点延迟时间（毫秒）
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.FocusManagementBehavior.NextFocusElementProperty">
            <summary>
            下一个焦点元素
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.FocusManagementBehavior.PreviousFocusElementProperty">
            <summary>
            上一个焦点元素
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.FocusManagementBehavior.FocusOnLoad">
            <summary>
            获取或设置是否在加载时自动获取焦点
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.FocusManagementBehavior.FocusOnMouseEnter">
            <summary>
            获取或设置是否在鼠标进入时获取焦点
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.FocusManagementBehavior.SelectAllOnFocus">
            <summary>
            获取或设置是否选择所有文本
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.FocusManagementBehavior.TabIndex">
            <summary>
            获取或设置Tab键导航顺序
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.FocusManagementBehavior.IsTabStop">
            <summary>
            获取或设置是否参与Tab键导航
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.FocusManagementBehavior.FocusDelay">
            <summary>
            获取或设置焦点延迟时间
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.FocusManagementBehavior.NextFocusElement">
            <summary>
            获取或设置下一个焦点元素
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.FocusManagementBehavior.PreviousFocusElement">
            <summary>
            获取或设置上一个焦点元素
            </summary>
        </member>
        <member name="E:FluentSystemDesign.WPF.Behaviors.FocusManagementBehavior.FocusReceived">
            <summary>
            获得焦点事件
            </summary>
        </member>
        <member name="E:FluentSystemDesign.WPF.Behaviors.FocusManagementBehavior.FocusLost">
            <summary>
            失去焦点事件
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.FocusManagementBehavior.OnAttached">
            <summary>
            附加到关联对象时调用
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.FocusManagementBehavior.OnDetaching">
            <summary>
            从关联对象分离时调用
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.FocusManagementBehavior.SetFocus">
            <summary>
            设置焦点到关联对象
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.FocusManagementBehavior.MoveFocusToNext">
            <summary>
            移动焦点到下一个元素
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.FocusManagementBehavior.MoveFocusToPrevious">
            <summary>
            移动焦点到上一个元素
            </summary>
        </member>
        <member name="T:FluentSystemDesign.WPF.Behaviors.FocusEventArgs">
            <summary>
            焦点事件参数
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.FocusEventArgs.Element">
            <summary>
            焦点元素
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.FocusEventArgs.#ctor(System.Windows.FrameworkElement)">
            <summary>
            构造函数
            </summary>
            <param name="element">焦点元素</param>
        </member>
        <member name="T:FluentSystemDesign.WPF.Behaviors.ScrollBehavior">
            <summary>
            滚动行为，为ScrollViewer提供增强的滚动功能
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.ScrollBehavior.IsSmoothScrollEnabledProperty">
            <summary>
            是否启用平滑滚动
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.ScrollBehavior.ScrollDurationProperty">
            <summary>
            滚动动画持续时间
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.ScrollBehavior.ScrollEasingFunctionProperty">
            <summary>
            滚动缓动函数
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.ScrollBehavior.MouseWheelStepProperty">
            <summary>
            鼠标滚轮滚动步长
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.ScrollBehavior.AutoHideScrollBarProperty">
            <summary>
            是否启用自动隐藏滚动条
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.ScrollBehavior.ScrollBarHideDelayProperty">
            <summary>
            滚动条自动隐藏延迟时间
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.ScrollBehavior.EnableBounceEffectProperty">
            <summary>
            是否启用边界反弹效果
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.ScrollBehavior.BounceIntensityProperty">
            <summary>
            反弹强度
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.ScrollBehavior.IsSmoothScrollEnabled">
            <summary>
            获取或设置是否启用平滑滚动
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.ScrollBehavior.ScrollDuration">
            <summary>
            获取或设置滚动动画持续时间
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.ScrollBehavior.ScrollEasingFunction">
            <summary>
            获取或设置滚动缓动函数
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.ScrollBehavior.MouseWheelStep">
            <summary>
            获取或设置鼠标滚轮滚动步长
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.ScrollBehavior.AutoHideScrollBar">
            <summary>
            获取或设置是否启用自动隐藏滚动条
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.ScrollBehavior.ScrollBarHideDelay">
            <summary>
            获取或设置滚动条自动隐藏延迟时间
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.ScrollBehavior.EnableBounceEffect">
            <summary>
            获取或设置是否启用边界反弹效果
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.ScrollBehavior.BounceIntensity">
            <summary>
            获取或设置反弹强度
            </summary>
        </member>
        <member name="E:FluentSystemDesign.WPF.Behaviors.ScrollBehavior.ScrollStarted">
            <summary>
            滚动开始事件
            </summary>
        </member>
        <member name="E:FluentSystemDesign.WPF.Behaviors.ScrollBehavior.ScrollCompleted">
            <summary>
            滚动结束事件
            </summary>
        </member>
        <member name="E:FluentSystemDesign.WPF.Behaviors.ScrollBehavior.ReachedTop">
            <summary>
            到达顶部事件
            </summary>
        </member>
        <member name="E:FluentSystemDesign.WPF.Behaviors.ScrollBehavior.ReachedBottom">
            <summary>
            到达底部事件
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.ScrollBehavior.OnAttached">
            <summary>
            附加到关联对象时调用
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.ScrollBehavior.OnDetaching">
            <summary>
            从关联对象分离时调用
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.ScrollBehavior.ScrollToVerticalOffset(System.Double)">
            <summary>
            平滑滚动到指定垂直位置
            </summary>
            <param name="offset">垂直偏移量</param>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.ScrollBehavior.ScrollToHorizontalOffset(System.Double)">
            <summary>
            平滑滚动到指定水平位置
            </summary>
            <param name="offset">水平偏移量</param>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.ScrollBehavior.ScrollToTop">
            <summary>
            滚动到顶部
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.ScrollBehavior.ScrollToBottom">
            <summary>
            滚动到底部
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.ScrollBehavior.ScrollToLeft">
            <summary>
            滚动到左侧
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.ScrollBehavior.ScrollToRight">
            <summary>
            滚动到右侧
            </summary>
        </member>
        <member name="T:FluentSystemDesign.WPF.Behaviors.ScrollEventArgs">
            <summary>
            滚动事件参数
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.ScrollEventArgs.FromOffset">
            <summary>
            起始位置
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.ScrollEventArgs.ToOffset">
            <summary>
            目标位置
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.ScrollEventArgs.Direction">
            <summary>
            滚动方向
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.ScrollEventArgs.#ctor(System.Double,System.Double,FluentSystemDesign.WPF.Behaviors.ScrollDirection)">
            <summary>
            构造函数
            </summary>
            <param name="fromOffset">起始位置</param>
            <param name="toOffset">目标位置</param>
            <param name="direction">滚动方向</param>
        </member>
        <member name="T:FluentSystemDesign.WPF.Behaviors.ScrollDirection">
            <summary>
            滚动方向枚举
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.ScrollDirection.Vertical">
            <summary>
            垂直滚动
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.ScrollDirection.Horizontal">
            <summary>
            水平滚动
            </summary>
        </member>
        <member name="T:FluentSystemDesign.WPF.Behaviors.ValidationBehavior">
            <summary>
            输入验证行为，为输入控件提供实时验证功能
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.ValidationBehavior.ValidationTypeProperty">
            <summary>
            验证规则类型
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.ValidationBehavior.RegexPatternProperty">
            <summary>
            正则表达式模式
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.ValidationBehavior.MinLengthProperty">
            <summary>
            最小长度
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.ValidationBehavior.MaxLengthProperty">
            <summary>
            最大长度
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.ValidationBehavior.IsRequiredProperty">
            <summary>
            是否必填
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.ValidationBehavior.ErrorMessageProperty">
            <summary>
            错误消息
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.ValidationBehavior.ValidateOnTextChangedProperty">
            <summary>
            是否实时验证
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.ValidationBehavior.ErrorBorderBrushProperty">
            <summary>
            错误边框颜色
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.ValidationBehavior.NormalBorderBrushProperty">
            <summary>
            正常边框颜色
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.ValidationBehavior.ValidationType">
            <summary>
            获取或设置验证规则类型
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.ValidationBehavior.RegexPattern">
            <summary>
            获取或设置正则表达式模式
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.ValidationBehavior.MinLength">
            <summary>
            获取或设置最小长度
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.ValidationBehavior.MaxLength">
            <summary>
            获取或设置最大长度
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.ValidationBehavior.IsRequired">
            <summary>
            获取或设置是否必填
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.ValidationBehavior.ErrorMessage">
            <summary>
            获取或设置错误消息
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.ValidationBehavior.ValidateOnTextChanged">
            <summary>
            获取或设置是否实时验证
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.ValidationBehavior.ErrorBorderBrush">
            <summary>
            获取或设置错误边框颜色
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.ValidationBehavior.NormalBorderBrush">
            <summary>
            获取或设置正常边框颜色
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.ValidationBehavior.IsValid">
            <summary>
            获取当前验证状态
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.ValidationBehavior.CurrentError">
            <summary>
            获取当前错误信息
            </summary>
        </member>
        <member name="E:FluentSystemDesign.WPF.Behaviors.ValidationBehavior.ValidationChanged">
            <summary>
            验证状态改变事件
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.ValidationBehavior.OnAttached">
            <summary>
            附加到关联对象时调用
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.ValidationBehavior.OnDetaching">
            <summary>
            从关联对象分离时调用
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.ValidationBehavior.Validate">
            <summary>
            手动验证
            </summary>
            <returns>验证是否通过</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.ValidationBehavior.ClearValidation">
            <summary>
            清除验证状态
            </summary>
        </member>
        <member name="T:FluentSystemDesign.WPF.Behaviors.ValidationType">
            <summary>
            验证类型枚举
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.ValidationType.None">
            <summary>
            无验证
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.ValidationType.Email">
            <summary>
            邮箱验证
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.ValidationType.Phone">
            <summary>
            手机号验证
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.ValidationType.Number">
            <summary>
            数字验证
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.ValidationType.Decimal">
            <summary>
            小数验证
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.ValidationType.Url">
            <summary>
            网址验证
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.ValidationType.Chinese">
            <summary>
            中文验证
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.ValidationType.English">
            <summary>
            英文验证
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.ValidationType.AlphaNumeric">
            <summary>
            字母数字验证
            </summary>
        </member>
        <member name="T:FluentSystemDesign.WPF.Behaviors.ValidationChangedEventArgs">
            <summary>
            验证状态改变事件参数
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.ValidationChangedEventArgs.IsValid">
            <summary>
            是否验证通过
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.ValidationChangedEventArgs.ErrorMessage">
            <summary>
            错误消息
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.ValidationChangedEventArgs.#ctor(System.Boolean,System.String)">
            <summary>
            构造函数
            </summary>
            <param name="isValid">是否验证通过</param>
            <param name="errorMessage">错误消息</param>
        </member>
        <member name="T:FluentSystemDesign.WPF.Behaviors.WatermarkBehavior">
            <summary>
            水印行为，为文本输入控件提供水印功能
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.WatermarkBehavior.WatermarkTextProperty">
            <summary>
            水印文本
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.WatermarkBehavior.WatermarkFontSizeProperty">
            <summary>
            水印字体大小
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.WatermarkBehavior.WatermarkFontStyleProperty">
            <summary>
            水印字体样式
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.WatermarkBehavior.WatermarkFontWeightProperty">
            <summary>
            水印字体粗细
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.WatermarkBehavior.WatermarkForegroundProperty">
            <summary>
            水印前景色
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.WatermarkBehavior.WatermarkOpacityProperty">
            <summary>
            水印透明度
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.WatermarkBehavior.WatermarkHorizontalAlignmentProperty">
            <summary>
            水印水平对齐方式
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.WatermarkBehavior.WatermarkVerticalAlignmentProperty">
            <summary>
            水印垂直对齐方式
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.WatermarkBehavior.WatermarkMarginProperty">
            <summary>
            水印边距
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.WatermarkBehavior.HideOnFocusProperty">
            <summary>
            是否在获得焦点时隐藏水印
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.WatermarkBehavior.WatermarkText">
            <summary>
            获取或设置水印文本
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.WatermarkBehavior.WatermarkFontSize">
            <summary>
            获取或设置水印字体大小
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.WatermarkBehavior.WatermarkFontStyle">
            <summary>
            获取或设置水印字体样式
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.WatermarkBehavior.WatermarkFontWeight">
            <summary>
            获取或设置水印字体粗细
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.WatermarkBehavior.WatermarkForeground">
            <summary>
            获取或设置水印前景色
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.WatermarkBehavior.WatermarkOpacity">
            <summary>
            获取或设置水印透明度
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.WatermarkBehavior.WatermarkHorizontalAlignment">
            <summary>
            获取或设置水印水平对齐方式
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.WatermarkBehavior.WatermarkVerticalAlignment">
            <summary>
            获取或设置水印垂直对齐方式
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.WatermarkBehavior.WatermarkMargin">
            <summary>
            获取或设置水印边距
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.WatermarkBehavior.HideOnFocus">
            <summary>
            获取或设置是否在获得焦点时隐藏水印
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.WatermarkBehavior.OnAttached">
            <summary>
            附加到关联对象时调用
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.WatermarkBehavior.OnDetaching">
            <summary>
            从关联对象分离时调用
            </summary>
        </member>
        <member name="T:FluentSystemDesign.WPF.Behaviors.WatermarkAdorner">
            <summary>
            水印装饰器
            </summary>
        </member>
        <member name="T:FluentSystemDesign.WPF.Behaviors.WindowBehavior">
            <summary>
            窗口行为，为Window提供增强功能
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.WindowBehavior.EnableDragMoveProperty">
            <summary>
            是否启用拖拽移动
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.WindowBehavior.EnableDoubleClickMaximizeProperty">
            <summary>
            是否启用双击最大化/还原
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.WindowBehavior.SaveWindowPlacementProperty">
            <summary>
            是否保存窗口位置和大小
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.WindowBehavior.PlacementKeyProperty">
            <summary>
            窗口位置保存键
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.WindowBehavior.EnableFadeEffectProperty">
            <summary>
            是否启用窗口淡入淡出效果
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.WindowBehavior.FadeDurationProperty">
            <summary>
            淡入淡出持续时间
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.WindowBehavior.HideMinimizeButtonProperty">
            <summary>
            是否隐藏最小化按钮
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.WindowBehavior.HideMaximizeButtonProperty">
            <summary>
            是否隐藏最大化按钮
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.WindowBehavior.HideCloseButtonProperty">
            <summary>
            是否隐藏关闭按钮
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.WindowBehavior.EnableDropShadowProperty">
            <summary>
            是否启用窗口阴影
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Behaviors.WindowBehavior.EnableEscapeKeyCloseProperty">
            <summary>
            是否启用ESC键关闭窗口
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.WindowBehavior.EnableDragMove">
            <summary>
            获取或设置是否启用拖拽移动
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.WindowBehavior.EnableDoubleClickMaximize">
            <summary>
            获取或设置是否启用双击最大化/还原
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.WindowBehavior.SaveWindowPlacement">
            <summary>
            获取或设置是否保存窗口位置和大小
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.WindowBehavior.PlacementKey">
            <summary>
            获取或设置窗口位置保存键
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.WindowBehavior.EnableFadeEffect">
            <summary>
            获取或设置是否启用窗口淡入淡出效果
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.WindowBehavior.FadeDuration">
            <summary>
            获取或设置淡入淡出持续时间
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.WindowBehavior.HideMinimizeButton">
            <summary>
            获取或设置是否隐藏最小化按钮
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.WindowBehavior.HideMaximizeButton">
            <summary>
            获取或设置是否隐藏最大化按钮
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.WindowBehavior.HideCloseButton">
            <summary>
            获取或设置是否隐藏关闭按钮
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.WindowBehavior.EnableDropShadow">
            <summary>
            获取或设置是否启用窗口阴影
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.WindowBehavior.EnableEscapeKeyClose">
            <summary>
            获取或设置是否启用ESC键关闭窗口
            </summary>
        </member>
        <member name="E:FluentSystemDesign.WPF.Behaviors.WindowBehavior.WindowShowing">
            <summary>
            窗口显示前事件
            </summary>
        </member>
        <member name="E:FluentSystemDesign.WPF.Behaviors.WindowBehavior.WindowClosing">
            <summary>
            窗口关闭前事件
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.WindowBehavior.OnAttached">
            <summary>
            附加到关联对象时调用
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.WindowBehavior.OnDetaching">
            <summary>
            从关联对象分离时调用
            </summary>
        </member>
        <member name="T:FluentSystemDesign.WPF.Behaviors.WindowEventArgs">
            <summary>
            窗口事件参数
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.WindowEventArgs.Window">
            <summary>
            窗口对象
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.WindowEventArgs.#ctor(System.Windows.Window)">
            <summary>
            构造函数
            </summary>
            <param name="window">窗口对象</param>
        </member>
        <member name="T:FluentSystemDesign.WPF.Behaviors.WindowClosingEventArgs">
            <summary>
            窗口关闭事件参数
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Behaviors.WindowClosingEventArgs.Cancel">
            <summary>
            是否取消关闭
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Behaviors.WindowClosingEventArgs.#ctor(System.Windows.Window)">
            <summary>
            构造函数
            </summary>
            <param name="window">窗口对象</param>
        </member>
        <member name="T:FluentSystemDesign.WPF.Controls.ColorPalette">
            <summary>
            色彩调色板控件，用于展示色彩系统
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Controls.ColorPalette.Title">
            <summary>
            调色板标题
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Controls.ColorPalette.TitleProperty">
            <summary>
            标识 Title 依赖属性
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Controls.ColorPalette.ColorItems">
            <summary>
            色彩项集合
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Controls.ColorPalette.ColorItemsProperty">
            <summary>
            标识 ColorItems 依赖属性
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Controls.ColorPalette.#ctor">
            <summary>
            初始化 ColorPalette 类的新实例
            </summary>
        </member>
        <member name="T:FluentSystemDesign.WPF.Controls.ColorItem">
            <summary>
            色彩项
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Controls.ColorItem.Name">
            <summary>
            色彩名称
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Controls.ColorItem.NameProperty">
            <summary>
            标识 Name 依赖属性
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Controls.ColorItem.Color">
            <summary>
            色彩值
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Controls.ColorItem.ColorProperty">
            <summary>
            标识 Color 依赖属性
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Controls.ColorItem.Brush">
            <summary>
            色彩画刷
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Controls.ColorItem.BrushProperty">
            <summary>
            标识 Brush 依赖属性
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Controls.ColorItem.HexValue">
            <summary>
            十六进制色值
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Controls.ColorItem.HexValueProperty">
            <summary>
            标识 HexValue 依赖属性
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Controls.ColorItem.IsDark">
            <summary>
            是否为深色
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Controls.ColorItem.IsDarkProperty">
            <summary>
            标识 IsDark 依赖属性
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Controls.ColorItem.#ctor">
            <summary>
            初始化 ColorItem 类的新实例
            </summary>
        </member>
        <member name="T:FluentSystemDesign.WPF.Controls.ColorItemCollection">
            <summary>
            色彩项集合
            </summary>
        </member>
        <member name="T:FluentSystemDesign.WPF.Controls.StringToVisibilityConverter">
            <summary>
            字符串到可见性转换器
            空字符串或null转换为Collapsed，非空字符串转换为Visible
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Controls.StringToVisibilityConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            将字符串转换为可见性
            </summary>
            <param name="value">字符串值</param>
            <param name="targetType">目标类型</param>
            <param name="parameter">参数</param>
            <param name="culture">文化信息</param>
            <returns>可见性值</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Controls.StringToVisibilityConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            将可见性转换为字符串（不支持）
            </summary>
            <param name="value">可见性值</param>
            <param name="targetType">目标类型</param>
            <param name="parameter">参数</param>
            <param name="culture">文化信息</param>
            <returns>不支持的操作异常</returns>
        </member>
        <member name="T:FluentSystemDesign.WPF.Converters.TextCaseConverter">
            <summary>
            文本大小写转换器
            用于在XAML中实现文本的大小写转换功能
            </summary>
            <remarks>
            由于WPF的TextBlock不支持CSS的text-transform属性，
            此转换器提供了在绑定时进行文本大小写转换的功能。
            </remarks>
            <example>
            在XAML中使用：
            <code>
            &lt;TextBlock Text="{Binding SomeText, Converter={StaticResource TextCaseConverter}, ConverterParameter=upper}"/&gt;
            </code>
            </example>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.TextCaseConverter.Convert(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            将文本转换为指定的大小写格式
            </summary>
            <param name="value">要转换的文本值</param>
            <param name="targetType">目标类型（通常为string）</param>
            <param name="parameter">转换参数：upper（大写）、lower（小写）、title（标题格式）</param>
            <param name="culture">区域性信息</param>
            <returns>转换后的文本</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.TextCaseConverter.ConvertBack(System.Object,System.Type,System.Object,System.Globalization.CultureInfo)">
            <summary>
            反向转换（不支持）
            </summary>
            <param name="value">值</param>
            <param name="targetType">目标类型</param>
            <param name="parameter">参数</param>
            <param name="culture">区域性信息</param>
            <returns>抛出NotImplementedException</returns>
            <exception cref="T:System.NotImplementedException">此转换器不支持反向转换</exception>
        </member>
        <member name="M:FluentSystemDesign.WPF.Converters.TextCaseConverter.CapitalizeFirstLetter(System.String,System.Globalization.CultureInfo)">
            <summary>
            将文本的首字母大写
            </summary>
            <param name="text">要处理的文本</param>
            <param name="culture">区域性信息</param>
            <returns>首字母大写的文本</returns>
        </member>
        <member name="T:FluentSystemDesign.WPF.Helpers.ThemeManager">
            <summary>
            主题管理器，用于管理应用程序的主题切换
            </summary>
        </member>
        <member name="T:FluentSystemDesign.WPF.Helpers.ThemeManager.ThemeType">
            <summary>
            主题类型枚举
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Helpers.ThemeManager.ThemeType.Light">
            <summary>
            浅色主题
            </summary>
        </member>
        <member name="F:FluentSystemDesign.WPF.Helpers.ThemeManager.ThemeType.Dark">
            <summary>
            深色主题
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Helpers.ThemeManager.CurrentTheme">
            <summary>
            当前主题类型
            </summary>
        </member>
        <member name="E:FluentSystemDesign.WPF.Helpers.ThemeManager.ThemeChanged">
            <summary>
            主题变更事件
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Helpers.ThemeManager.ApplyTheme(System.Windows.Application,FluentSystemDesign.WPF.Helpers.ThemeManager.ThemeType)">
            <summary>
            应用主题到指定的应用程序
            </summary>
            <param name="app">目标应用程序</param>
            <param name="theme">要应用的主题类型</param>
        </member>
        <member name="M:FluentSystemDesign.WPF.Helpers.ThemeManager.ToggleTheme(System.Windows.Application)">
            <summary>
            切换主题（在浅色和深色之间切换）
            </summary>
            <param name="app">目标应用程序</param>
        </member>
        <member name="M:FluentSystemDesign.WPF.Helpers.ThemeManager.GetSystemTheme">
            <summary>
            获取系统主题偏好
            </summary>
            <returns>系统推荐的主题类型</returns>
        </member>
        <member name="M:FluentSystemDesign.WPF.Helpers.ThemeManager.RemoveThemeResources(System.Windows.Application)">
            <summary>
            移除主题资源
            </summary>
            <param name="app">目标应用程序</param>
        </member>
        <member name="M:FluentSystemDesign.WPF.Helpers.ThemeManager.AddThemeResources(System.Windows.Application,FluentSystemDesign.WPF.Helpers.ThemeManager.ThemeType)">
            <summary>
            添加主题资源
            </summary>
            <param name="app">目标应用程序</param>
            <param name="theme">主题类型</param>
        </member>
        <member name="T:FluentSystemDesign.WPF.Helpers.ThemeChangedEventArgs">
            <summary>
            主题变更事件参数
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Helpers.ThemeChangedEventArgs.OldTheme">
            <summary>
            旧主题
            </summary>
        </member>
        <member name="P:FluentSystemDesign.WPF.Helpers.ThemeChangedEventArgs.NewTheme">
            <summary>
            新主题
            </summary>
        </member>
        <member name="M:FluentSystemDesign.WPF.Helpers.ThemeChangedEventArgs.#ctor(FluentSystemDesign.WPF.Helpers.ThemeManager.ThemeType,FluentSystemDesign.WPF.Helpers.ThemeManager.ThemeType)">
            <summary>
            构造函数
            </summary>
            <param name="oldTheme">旧主题</param>
            <param name="newTheme">新主题</param>
        </member>
    </members>
</doc>
