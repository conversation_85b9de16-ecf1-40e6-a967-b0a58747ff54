using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace FluentSystemDesign.WPF.Converters
{
    /// <summary>
    /// 数量到可见性转换器
    /// 根据数值与阈值的比较结果来控制元素的可见性
    /// </summary>
    /// <remarks>
    /// 支持的比较操作：
    /// - ">" 或 "GreaterThan": 大于指定值时显示
    /// - ">=" 或 "GreaterThanOrEqual": 大于等于指定值时显示
    /// - "<" 或 "LessThan": 小于指定值时显示
    /// - "<=" 或 "LessThanOrEqual": 小于等于指定值时显示
    /// - "=" 或 "Equal": 等于指定值时显示
    /// - "!=" 或 "NotEqual": 不等于指定值时显示
    /// - "Range": 在指定范围内时显示，格式："Range:min,max"
    /// 
    /// 其他选项：
    /// - "Hidden": 使用Hidden而不是Collapsed
    /// - "Invert": 反转结果
    /// </remarks>
    /// <example>
    /// 在XAML中使用：
    /// <code>
    /// &lt;TextBlock Visibility="{Binding ItemCount, Converter={StaticResource CountToVisibilityConverter}, ConverterParameter=>0}"/&gt;
    /// &lt;Button Visibility="{Binding SelectedCount, Converter={StaticResource CountToVisibilityConverter}, ConverterParameter=>=5}"/&gt;
    /// &lt;ProgressBar Visibility="{Binding Progress, Converter={StaticResource CountToVisibilityConverter}, ConverterParameter=Range:1,99}"/&gt;
    /// </code>
    /// </example>
    [ValueConversion(typeof(int), typeof(Visibility))]
    [ValueConversion(typeof(double), typeof(Visibility))]
    [ValueConversion(typeof(decimal), typeof(Visibility))]
    [ValueConversion(typeof(float), typeof(Visibility))]
    public class CountToVisibilityConverter : IValueConverter
    {
        /// <summary>
        /// 默认的隐藏方式
        /// </summary>
        public Visibility DefaultHiddenVisibility { get; set; } = Visibility.Collapsed;

        /// <summary>
        /// 默认的比较操作
        /// </summary>
        public ComparisonOperation DefaultOperation { get; set; } = ComparisonOperation.GreaterThan;

        /// <summary>
        /// 默认的比较值
        /// </summary>
        public double DefaultThreshold { get; set; } = 0;

        /// <summary>
        /// 将数值转换为可见性
        /// </summary>
        /// <param name="value">数值</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">比较参数</param>
        /// <param name="culture">文化信息</param>
        /// <returns>可见性值</returns>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (!TryConvertToDouble(value, out double numericValue))
            {
                return DefaultHiddenVisibility;
            }

            var (operation, threshold, maxThreshold, hiddenVisibility, isInverted) = ParseParameter(parameter?.ToString(), culture);

            // 执行比较操作
            var result = operation switch
            {
                ComparisonOperation.GreaterThan => numericValue > threshold,
                ComparisonOperation.GreaterThanOrEqual => numericValue >= threshold,
                ComparisonOperation.LessThan => numericValue < threshold,
                ComparisonOperation.LessThanOrEqual => numericValue <= threshold,
                ComparisonOperation.Equal => Math.Abs(numericValue - threshold) < double.Epsilon,
                ComparisonOperation.NotEqual => Math.Abs(numericValue - threshold) >= double.Epsilon,
                ComparisonOperation.Range => numericValue >= threshold && numericValue <= (maxThreshold ?? threshold),
                _ => false
            };

            // 应用反转逻辑
            if (isInverted)
            {
                result = !result;
            }

            return result ? Visibility.Visible : hiddenVisibility;
        }

        /// <summary>
        /// 反向转换（不支持）
        /// </summary>
        /// <param name="value">可见性值</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">转换参数</param>
        /// <param name="culture">文化信息</param>
        /// <returns>不支持的操作异常</returns>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotSupportedException("CountToVisibilityConverter does not support ConvertBack operation.");
        }

        /// <summary>
        /// 尝试将对象转换为double
        /// </summary>
        /// <param name="value">要转换的值</param>
        /// <param name="result">转换结果</param>
        /// <returns>是否转换成功</returns>
        private static bool TryConvertToDouble(object value, out double result)
        {
            result = 0;

            return value switch
            {
                double d => (result = d) == d,
                float f => (result = f) == f,
                decimal dec => (result = (double)dec) == (double)dec,
                int i => (result = i) == i,
                long l => (result = l) == l,
                short s => (result = s) == s,
                byte b => (result = b) == b,
                uint ui => (result = ui) == ui,
                ulong ul => (result = ul) == ul,
                ushort us => (result = us) == us,
                sbyte sb => (result = sb) == sb,
                string str => double.TryParse(str, out result),
                _ => false
            };
        }

        /// <summary>
        /// 解析参数字符串
        /// </summary>
        /// <param name="parameter">参数字符串</param>
        /// <param name="culture">文化信息</param>
        /// <returns>解析结果</returns>
        private (ComparisonOperation operation, double threshold, double? maxThreshold, Visibility hiddenVisibility, bool isInverted) ParseParameter(string parameter, CultureInfo culture)
        {
            var operation = DefaultOperation;
            var threshold = DefaultThreshold;
            double? maxThreshold = null;
            var hiddenVisibility = DefaultHiddenVisibility;
            var isInverted = false;

            if (string.IsNullOrWhiteSpace(parameter))
            {
                return (operation, threshold, maxThreshold, hiddenVisibility, isInverted);
            }

            var lowerParam = parameter.ToLowerInvariant();

            // 检查反转标志
            if (lowerParam.Contains("invert"))
            {
                isInverted = true;
                parameter = parameter.Replace("invert", "", StringComparison.OrdinalIgnoreCase).Trim();
            }

            // 检查隐藏方式
            if (lowerParam.Contains("hidden"))
            {
                hiddenVisibility = Visibility.Hidden;
                parameter = parameter.Replace("hidden", "", StringComparison.OrdinalIgnoreCase).Trim();
            }

            // 解析比较操作和阈值
            if (parameter.StartsWith("Range:", StringComparison.OrdinalIgnoreCase))
            {
                operation = ComparisonOperation.Range;
                var rangeValues = parameter.Substring(6).Split(',');
                if (rangeValues.Length >= 2)
                {
                    if (double.TryParse(rangeValues[0].Trim(), NumberStyles.Float, culture, out double min))
                    {
                        threshold = min;
                    }
                    if (double.TryParse(rangeValues[1].Trim(), NumberStyles.Float, culture, out double max))
                    {
                        maxThreshold = max;
                    }
                }
            }
            else if (parameter.StartsWith(">="))
            {
                operation = ComparisonOperation.GreaterThanOrEqual;
                if (double.TryParse(parameter.Substring(2).Trim(), NumberStyles.Float, culture, out double value))
                {
                    threshold = value;
                }
            }
            else if (parameter.StartsWith("<="))
            {
                operation = ComparisonOperation.LessThanOrEqual;
                if (double.TryParse(parameter.Substring(2).Trim(), NumberStyles.Float, culture, out double value))
                {
                    threshold = value;
                }
            }
            else if (parameter.StartsWith("!="))
            {
                operation = ComparisonOperation.NotEqual;
                if (double.TryParse(parameter.Substring(2).Trim(), NumberStyles.Float, culture, out double value))
                {
                    threshold = value;
                }
            }
            else if (parameter.StartsWith(">"))
            {
                operation = ComparisonOperation.GreaterThan;
                if (double.TryParse(parameter.Substring(1).Trim(), NumberStyles.Float, culture, out double value))
                {
                    threshold = value;
                }
            }
            else if (parameter.StartsWith("<"))
            {
                operation = ComparisonOperation.LessThan;
                if (double.TryParse(parameter.Substring(1).Trim(), NumberStyles.Float, culture, out double value))
                {
                    threshold = value;
                }
            }
            else if (parameter.StartsWith("="))
            {
                operation = ComparisonOperation.Equal;
                if (double.TryParse(parameter.Substring(1).Trim(), NumberStyles.Float, culture, out double value))
                {
                    threshold = value;
                }
            }
            else if (double.TryParse(parameter.Trim(), NumberStyles.Float, culture, out double directValue))
            {
                // 如果只是一个数字，使用默认操作
                threshold = directValue;
            }

            return (operation, threshold, maxThreshold, hiddenVisibility, isInverted);
        }

        /// <summary>
        /// 比较操作类型
        /// </summary>
        public enum ComparisonOperation
        {
            GreaterThan,
            GreaterThanOrEqual,
            LessThan,
            LessThanOrEqual,
            Equal,
            NotEqual,
            Range
        }
    }
}
