using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;
using System.Windows.Media;

namespace FluentSystemDesign.WPF.Converters
{
    /// <summary>
    /// 颜色对比度转换器
    /// 根据输入颜色的亮度自动选择对比度最佳的前景色（通常是黑色或白色）
    /// </summary>
    /// <remarks>
    /// 使用相对亮度计算公式来确定颜色的亮度，然后选择对比度最佳的前景色。
    /// 默认行为：亮色背景返回黑色，暗色背景返回白色。
    /// 可以通过参数自定义返回的颜色。
    /// </remarks>
    /// <example>
    /// 在XAML中使用：
    /// <code>
    /// &lt;TextBlock Foreground="{Binding BackgroundColor, Converter={StaticResource ColorToContrastConverter}}"/&gt;
    /// &lt;TextBlock Foreground="{Binding BackgroundColor, Converter={StaticResource ColorToContrastConverter}, ConverterParameter=Light=#FFFFFF,Dark=#000000}"/&gt;
    /// </code>
    /// </example>
    [ValueConversion(typeof(Color), typeof(Color))]
    [ValueConversion(typeof(Brush), typeof(Color))]
    [ValueConversion(typeof(string), typeof(Color))]
    public class ColorToContrastConverter : IValueConverter
    {
        /// <summary>
        /// 亮度阈值（0-1），超过此值认为是亮色
        /// </summary>
        public double LuminanceThreshold { get; set; } = 0.5;

        /// <summary>
        /// 亮色背景时使用的前景色
        /// </summary>
        public Color LightBackgroundForeground { get; set; } = Colors.Black;

        /// <summary>
        /// 暗色背景时使用的前景色
        /// </summary>
        public Color DarkBackgroundForeground { get; set; } = Colors.White;

        /// <summary>
        /// 将颜色转换为对比色
        /// </summary>
        /// <param name="value">背景颜色（Color、Brush或十六进制字符串）</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">自定义颜色参数，格式："Light=#FFFFFF,Dark=#000000"</param>
        /// <param name="culture">文化信息</param>
        /// <returns>对比色</returns>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            Color backgroundColor;

            // 解析输入颜色
            if (value is Color color)
            {
                backgroundColor = color;
            }
            else if (value is SolidColorBrush brush)
            {
                backgroundColor = brush.Color;
            }
            else if (value is string hexString && !string.IsNullOrWhiteSpace(hexString))
            {
                if (!TryParseColor(hexString, out backgroundColor))
                {
                    return DependencyProperty.UnsetValue;
                }
            }
            else
            {
                return DependencyProperty.UnsetValue;
            }

            // 解析参数
            var (lightColor, darkColor) = ParseColorParameters(parameter?.ToString());

            // 计算相对亮度
            var luminance = CalculateRelativeLuminance(backgroundColor);

            // 根据亮度选择对比色
            var contrastColor = luminance > LuminanceThreshold ? lightColor : darkColor;

            // 如果目标类型是Brush，返回SolidColorBrush
            if (targetType == typeof(Brush) || targetType == typeof(SolidColorBrush))
            {
                return new SolidColorBrush(contrastColor);
            }

            return contrastColor;
        }

        /// <summary>
        /// 反向转换（不支持）
        /// </summary>
        /// <param name="value">值</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">参数</param>
        /// <param name="culture">文化信息</param>
        /// <returns>不支持的操作异常</returns>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotSupportedException("ColorToContrastConverter does not support ConvertBack operation.");
        }

        /// <summary>
        /// 计算颜色的相对亮度
        /// </summary>
        /// <param name="color">颜色</param>
        /// <returns>相对亮度（0-1）</returns>
        private static double CalculateRelativeLuminance(Color color)
        {
            // 将RGB值转换为0-1范围
            var r = color.R / 255.0;
            var g = color.G / 255.0;
            var b = color.B / 255.0;

            // 应用gamma校正
            r = r <= 0.03928 ? r / 12.92 : Math.Pow((r + 0.055) / 1.055, 2.4);
            g = g <= 0.03928 ? g / 12.92 : Math.Pow((g + 0.055) / 1.055, 2.4);
            b = b <= 0.03928 ? b / 12.92 : Math.Pow((b + 0.055) / 1.055, 2.4);

            // 计算相对亮度（基于人眼对不同颜色的敏感度）
            return 0.2126 * r + 0.7152 * g + 0.0722 * b;
        }

        /// <summary>
        /// 解析颜色参数
        /// </summary>
        /// <param name="parameter">参数字符串</param>
        /// <returns>亮色和暗色</returns>
        private (Color lightColor, Color darkColor) ParseColorParameters(string parameter)
        {
            var lightColor = LightBackgroundForeground;
            var darkColor = DarkBackgroundForeground;

            if (string.IsNullOrWhiteSpace(parameter))
            {
                return (lightColor, darkColor);
            }

            // 解析格式："Light=#FFFFFF,Dark=#000000"
            var parts = parameter.Split(',');
            foreach (var part in parts)
            {
                var keyValue = part.Split('=');
                if (keyValue.Length == 2)
                {
                    var key = keyValue[0].Trim().ToLowerInvariant();
                    var value = keyValue[1].Trim();

                    if (TryParseColor(value, out Color parsedColor))
                    {
                        switch (key)
                        {
                            case "light":
                                lightColor = parsedColor;
                                break;
                            case "dark":
                                darkColor = parsedColor;
                                break;
                        }
                    }
                }
            }

            return (lightColor, darkColor);
        }

        /// <summary>
        /// 尝试解析颜色字符串
        /// </summary>
        /// <param name="colorString">颜色字符串</param>
        /// <param name="color">解析出的颜色</param>
        /// <returns>是否解析成功</returns>
        private static bool TryParseColor(string colorString, out Color color)
        {
            color = Colors.Transparent;

            try
            {
                // 尝试使用ColorConverter解析
                var converter = new ColorConverter();
                if (converter.ConvertFromString(colorString) is Color parsedColor)
                {
                    color = parsedColor;
                    return true;
                }
            }
            catch
            {
                // 忽略异常，继续尝试其他方法
            }

            // 尝试解析十六进制格式
            if (colorString.StartsWith("#"))
            {
                return TryParseHexColor(colorString, out color);
            }

            // 尝试解析命名颜色
            try
            {
                var property = typeof(Colors).GetProperty(colorString);
                if (property != null && property.PropertyType == typeof(Color))
                {
                    color = (Color)property.GetValue(null);
                    return true;
                }
            }
            catch
            {
                // 忽略异常
            }

            return false;
        }

        /// <summary>
        /// 尝试解析十六进制颜色
        /// </summary>
        /// <param name="hex">十六进制颜色字符串</param>
        /// <param name="color">解析出的颜色</param>
        /// <returns>是否解析成功</returns>
        private static bool TryParseHexColor(string hex, out Color color)
        {
            color = Colors.Transparent;

            if (string.IsNullOrWhiteSpace(hex) || !hex.StartsWith("#"))
            {
                return false;
            }

            hex = hex.Substring(1); // 移除#

            try
            {
                switch (hex.Length)
                {
                    case 3: // RGB
                        color = Color.FromRgb(
                            System.Convert.ToByte(hex.Substring(0, 1) + hex.Substring(0, 1), 16),
                            System.Convert.ToByte(hex.Substring(1, 1) + hex.Substring(1, 1), 16),
                            System.Convert.ToByte(hex.Substring(2, 1) + hex.Substring(2, 1), 16));
                        return true;

                    case 6: // RRGGBB
                        color = Color.FromRgb(
                            System.Convert.ToByte(hex.Substring(0, 2), 16),
                            System.Convert.ToByte(hex.Substring(2, 2), 16),
                            System.Convert.ToByte(hex.Substring(4, 2), 16));
                        return true;

                    case 8: // AARRGGBB
                        color = Color.FromArgb(
                            System.Convert.ToByte(hex.Substring(0, 2), 16),
                            System.Convert.ToByte(hex.Substring(2, 2), 16),
                            System.Convert.ToByte(hex.Substring(4, 2), 16),
                            System.Convert.ToByte(hex.Substring(6, 2), 16));
                        return true;
                }
            }
            catch
            {
                // 忽略转换异常
            }

            return false;
        }
    }
}
