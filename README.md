# FluentSystemDesign WPF 控件库

[![.NET](https://img.shields.io/badge/.NET-8.0-blue.svg)](https://dotnet.microsoft.com/download)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![NuGet](https://img.shields.io/nuget/v/FluentSystemDesign.WPF.svg)](https://www.nuget.org/packages/FluentSystemDesign.WPF/)

一个基于微软 Fluent Design System 设计语言的现代化 WPF 控件库，为 WPF 应用程序提供美观、一致的用户界面组件。

## ✨ 特性

- 🎨 **现代设计**：基于 Microsoft Fluent Design System
- 🌙 **主题支持**：内置深色和浅色主题，支持主题切换
- 📱 **响应式**：支持不同屏幕尺寸和 DPI 设置
- ♿ **无障碍**：完整的辅助功能支持
- 🎯 **高性能**：优化的渲染性能和内存使用
- 🔧 **易定制**：灵活的样式系统，支持深度定制

## 🚀 快速开始

### 安装

```bash
# 通过 NuGet 包管理器
Install-Package FluentSystemDesign.WPF

# 通过 .NET CLI
dotnet add package FluentSystemDesign.WPF
```

### 基本使用

1. 在 App.xaml 中引用主题资源：

```xml
<Application.Resources>
    <ResourceDictionary>
        <ResourceDictionary.MergedDictionaries>
            <ResourceDictionary Source="pack://application:,,,/FluentSystemDesign.WPF;component/Themes/Styles.xaml"/>
        </ResourceDictionary.MergedDictionaries>
    </ResourceDictionary>
</Application.Resources>
```

2. 在 XAML 中使用控件：

```xml
<Window xmlns:fluent="clr-namespace:FluentSystemDesign.WPF;assembly=FluentSystemDesign.WPF">
    <fluent:FluentButton Content="Hello Fluent!" Style="{StaticResource PrimaryButton}"/>
</Window>
```

## 📚 控件文档

### 基础控件
- [Button](Docs/Controls/Button.md) - 按钮控件
- [TextBlock](Docs/Controls/TextBlock.md) - 文本显示控件
- [Image](Docs/Controls/Image.md) - 图像显示控件

### 输入控件
- [TextBox](Docs/Controls/TextBox.md) - 文本输入框
- [PasswordBox](Docs/Controls/PasswordBox.md) - 密码输入框
- [ComboBox](Docs/Controls/ComboBox.md) - 下拉选择框
- [CheckBox](Docs/Controls/CheckBox.md) - 复选框
- [RadioButton](Docs/Controls/RadioButton.md) - 单选按钮
- [Slider](Docs/Controls/Slider.md) - 滑块控件

### 导航控件
- [NavigationView](Docs/Controls/NavigationView.md) - 导航视图
- [TabView](Docs/Controls/TabView.md) - 标签页视图
- [Breadcrumb](Docs/Controls/Breadcrumb.md) - 面包屑导航

### 布局控件
- [Grid](Docs/Controls/Grid.md) - 网格布局
- [StackPanel](Docs/Controls/StackPanel.md) - 堆栈面板
- [WrapPanel](Docs/Controls/WrapPanel.md) - 自动换行面板
- [DockPanel](Docs/Controls/DockPanel.md) - 停靠面板

### 信息控件
- [InfoBar](Docs/Controls/InfoBar.md) - 信息栏
- [ProgressBar](Docs/Controls/ProgressBar.md) - 进度条
- [ProgressRing](Docs/Controls/ProgressRing.md) - 进度环
- [ToolTip](Docs/Controls/ToolTip.md) - 工具提示

## 🎨 设计系统

FluentSystemDesign.WPF 提供完整的设计系统：

### 主题系统
- **浅色主题**：适合日间使用的明亮主题
- **深色主题**：适合夜间使用的暗色主题
- **自定义主题**：支持创建自定义主题色彩方案

### 色彩系统
- **主色调**：完整的蓝色调色板（Primary）
- **次要色调**：紫色调色板（Secondary）
- **强调色**：橙色调色板（Accent）
- **语义化颜色**：成功、警告、错误、信息状态色
- **中性色系**：灰度调色板

### 字体系统
- **字体层级**：Display、H1-H6标题、Body正文、Caption说明
- **字体族**：Segoe UI主字体，支持中文和等宽字体
- **字体样式**：按钮、输入、导航、状态等专用样式
- **响应式字体**：支持不同屏幕尺寸的字体适配

详细信息请参阅：
- [色彩系统文档](Docs/ColorSystem.md)
- [字体系统文档](Docs/Typography.md)
- [主题定制指南](Docs/ThemeCustomization.md)

## 🔧 兼容性

- **.NET 版本**：.NET 8.0+
- **操作系统**：Windows 7 SP1+
- **开发环境**：Visual Studio 2022+

## 📖 文档

### 入门指南
- [快速入门指南](Docs/GettingStarted.md)
- [常见问题](Docs/FAQ.md)

### 设计系统
- [色彩系统](Docs/ColorSystem.md)
- [字体系统](Docs/Typography.md)
- [主题定制](Docs/ThemeCustomization.md)

### 控件文档
- [控件文档](Docs/Controls/)

## 🤝 贡献

我们欢迎社区贡献！请阅读 [贡献指南](CONTRIBUTING.md) 了解如何参与项目开发。

### 贡献方式

- 🐛 报告 Bug
- 💡 提出新功能建议
- 📝 改进文档
- 🔧 提交代码修复或新功能

## 📄 许可证

本项目采用 [MIT 许可证](LICENSE)。

## 🙏 致谢

感谢 Microsoft 的 Fluent Design System 为本项目提供设计指导。

---

**⭐ 如果这个项目对您有帮助，请给我们一个 Star！**
