using System;
using System.Globalization;
using System.Windows;
using System.Windows.Data;
using System.Windows.Media;

namespace FluentSystemDesign.WPF.Converters
{
    /// <summary>
    /// 十六进制字符串到颜色转换器
    /// 将十六进制颜色字符串转换为Color对象，支持多种格式
    /// </summary>
    /// <remarks>
    /// 支持的十六进制格式：
    /// - #RGB (例如: #F0A)
    /// - #RRGGBB (例如: #FF00AA)
    /// - #AARRGGBB (例如: #80FF00AA)
    /// - RGB (不带#前缀)
    /// - RRGGBB (不带#前缀)
    /// - AARRGGBB (不带#前缀)
    /// </remarks>
    /// <example>
    /// 在XAML中使用：
    /// <code>
    /// &lt;Rectangle Fill="{Binding HexColor, Converter={StaticResource HexToColorConverter}}"/&gt;
    /// &lt;Border BorderBrush="{Binding BorderHex, Converter={StaticResource HexToColorConverter}}"/&gt;
    /// </code>
    /// </example>
    [ValueConversion(typeof(string), typeof(Color))]
    public class HexToColorConverter : IValueConverter
    {
        /// <summary>
        /// 默认颜色（当转换失败时使用）
        /// </summary>
        public Color DefaultColor { get; set; } = Colors.Transparent;

        /// <summary>
        /// 将十六进制字符串转换为颜色
        /// </summary>
        /// <param name="value">十六进制颜色字符串</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">转换参数（未使用）</param>
        /// <param name="culture">文化信息</param>
        /// <returns>Color对象</returns>
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is not string hexString || string.IsNullOrWhiteSpace(hexString))
            {
                return DefaultColor;
            }

            if (TryParseHexColor(hexString.Trim(), out Color color))
            {
                return color;
            }

            return DefaultColor;
        }

        /// <summary>
        /// 将颜色转换为十六进制字符串
        /// </summary>
        /// <param name="value">Color对象</param>
        /// <param name="targetType">目标类型</param>
        /// <param name="parameter">转换参数：格式选项</param>
        /// <param name="culture">文化信息</param>
        /// <returns>十六进制字符串</returns>
        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            if (value is not Color color)
            {
                return DependencyProperty.UnsetValue;
            }

            var format = parameter?.ToString()?.ToLowerInvariant();
            
            return format switch
            {
                "noprefix" => ColorToHex(color, false),
                "short" => ColorToShortHex(color, true),
                "shortnoprefix" => ColorToShortHex(color, false),
                _ => ColorToHex(color, true)
            };
        }

        /// <summary>
        /// 尝试解析十六进制颜色字符串
        /// </summary>
        /// <param name="hex">十六进制字符串</param>
        /// <param name="color">解析出的颜色</param>
        /// <returns>是否解析成功</returns>
        private static bool TryParseHexColor(string hex, out Color color)
        {
            color = Colors.Transparent;

            // 移除可能的前缀
            if (hex.StartsWith("#"))
            {
                hex = hex.Substring(1);
            }
            else if (hex.StartsWith("0x", StringComparison.OrdinalIgnoreCase))
            {
                hex = hex.Substring(2);
            }

            // 验证字符是否都是有效的十六进制字符
            if (!IsValidHexString(hex))
            {
                return false;
            }

            try
            {
                switch (hex.Length)
                {
                    case 3: // RGB
                        {
                            var r = System.Convert.ToByte(hex.Substring(0, 1) + hex.Substring(0, 1), 16);
                            var g = System.Convert.ToByte(hex.Substring(1, 1) + hex.Substring(1, 1), 16);
                            var b = System.Convert.ToByte(hex.Substring(2, 1) + hex.Substring(2, 1), 16);
                            color = Color.FromRgb(r, g, b);
                            return true;
                        }

                    case 4: // ARGB
                        {
                            var a = System.Convert.ToByte(hex.Substring(0, 1) + hex.Substring(0, 1), 16);
                            var r = System.Convert.ToByte(hex.Substring(1, 1) + hex.Substring(1, 1), 16);
                            var g = System.Convert.ToByte(hex.Substring(2, 1) + hex.Substring(2, 1), 16);
                            var b = System.Convert.ToByte(hex.Substring(3, 1) + hex.Substring(3, 1), 16);
                            color = Color.FromArgb(a, r, g, b);
                            return true;
                        }

                    case 6: // RRGGBB
                        {
                            var r = System.Convert.ToByte(hex.Substring(0, 2), 16);
                            var g = System.Convert.ToByte(hex.Substring(2, 2), 16);
                            var b = System.Convert.ToByte(hex.Substring(4, 2), 16);
                            color = Color.FromRgb(r, g, b);
                            return true;
                        }

                    case 8: // AARRGGBB
                        {
                            var a = System.Convert.ToByte(hex.Substring(0, 2), 16);
                            var r = System.Convert.ToByte(hex.Substring(2, 2), 16);
                            var g = System.Convert.ToByte(hex.Substring(4, 2), 16);
                            var b = System.Convert.ToByte(hex.Substring(6, 2), 16);
                            color = Color.FromArgb(a, r, g, b);
                            return true;
                        }

                    default:
                        return false;
                }
            }
            catch (Exception ex) when (ex is FormatException || ex is OverflowException)
            {
                return false;
            }
        }

        /// <summary>
        /// 验证字符串是否为有效的十六进制字符串
        /// </summary>
        /// <param name="hex">十六进制字符串</param>
        /// <returns>是否有效</returns>
        private static bool IsValidHexString(string hex)
        {
            if (string.IsNullOrEmpty(hex))
            {
                return false;
            }

            foreach (char c in hex)
            {
                if (!((c >= '0' && c <= '9') || 
                      (c >= 'A' && c <= 'F') || 
                      (c >= 'a' && c <= 'f')))
                {
                    return false;
                }
            }

            return true;
        }

        /// <summary>
        /// 将颜色转换为十六进制字符串
        /// </summary>
        /// <param name="color">颜色</param>
        /// <param name="includePrefix">是否包含#前缀</param>
        /// <returns>十六进制字符串</returns>
        private static string ColorToHex(Color color, bool includePrefix)
        {
            var prefix = includePrefix ? "#" : "";
            
            if (color.A == 255)
            {
                return $"{prefix}{color.R:X2}{color.G:X2}{color.B:X2}";
            }
            else
            {
                return $"{prefix}{color.A:X2}{color.R:X2}{color.G:X2}{color.B:X2}";
            }
        }

        /// <summary>
        /// 将颜色转换为短格式十六进制字符串（如果可能）
        /// </summary>
        /// <param name="color">颜色</param>
        /// <param name="includePrefix">是否包含#前缀</param>
        /// <returns>十六进制字符串</returns>
        private static string ColorToShortHex(Color color, bool includePrefix)
        {
            var prefix = includePrefix ? "#" : "";

            // 检查是否可以使用短格式
            if (color.A == 255 && 
                CanUseShortFormat(color.R) && 
                CanUseShortFormat(color.G) && 
                CanUseShortFormat(color.B))
            {
                return $"{prefix}{color.R:X2}"[^1] + $"{color.G:X2}"[^1] + $"{color.B:X2}"[^1];
            }
            else if (CanUseShortFormat(color.A) && 
                     CanUseShortFormat(color.R) && 
                     CanUseShortFormat(color.G) && 
                     CanUseShortFormat(color.B))
            {
                return $"{prefix}{color.A:X2}"[^1] + $"{color.R:X2}"[^1] + $"{color.G:X2}"[^1] + $"{color.B:X2}"[^1];
            }

            // 如果不能使用短格式，返回完整格式
            return ColorToHex(color, includePrefix);
        }

        /// <summary>
        /// 检查字节值是否可以使用短格式表示
        /// </summary>
        /// <param name="value">字节值</param>
        /// <returns>是否可以使用短格式</returns>
        private static bool CanUseShortFormat(byte value)
        {
            // 短格式要求高4位和低4位相同
            return (value & 0x0F) == ((value & 0xF0) >> 4);
        }
    }
}
