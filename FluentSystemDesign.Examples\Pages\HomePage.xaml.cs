using System.Windows;
using System.Windows.Controls;

namespace FluentSystemDesign.Examples.Pages
{
    /// <summary>
    /// HomePage.xaml 的交互逻辑
    /// </summary>
    public partial class HomePage : Page
    {
        public HomePage()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 查看行为类示例按钮点击事件
        /// </summary>
        private void ViewBehaviors_Click(object sender, RoutedEventArgs e)
        {
            // 通知主窗口导航到行为类示例页面
            var mainWindow = Application.Current.MainWindow as MainWindow;
            mainWindow?.NavigateToBehaviorsPage();
        }

        /// <summary>
        /// 查看色彩系统按钮点击事件
        /// </summary>
        private void ViewColors_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("色彩系统将在后续版本中实现", "色彩系统", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// 查看文档按钮点击事件
        /// </summary>
        private void ViewDocumentation_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("完整文档将在后续版本中实现", "文档", MessageBoxButton.OK, MessageBoxImage.Information);
        }
    }
}
